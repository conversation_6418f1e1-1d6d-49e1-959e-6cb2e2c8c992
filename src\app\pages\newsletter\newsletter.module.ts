import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'

import { CommonToolsModule } from '@common/common-tools.module'
import { NewsletterFormComponent } from './newsletter-form/newsletter-form.component'
import { NewsletterRoutingModule } from './newsletter-routing.module'
import { NewsletterComponent } from './newsletter.component'
import { NewsletterListComponent } from './newsletter-list/newsletter-list.component'
import { NbBadgeModule } from '@nebular/theme';
import { NewsletterDetailComponent } from './newsletter-detail/newsletter-detail.component'

@NgModule({
  declarations: [
    NewsletterComponent,
    NewsletterFormComponent,
    NewsletterListComponent,
    NewsletterDetailComponent,
  ],
  imports: [
    CommonModule,
    NewsletterRoutingModule,
    CommonToolsModule,
    NbBadgeModule,
  ],
})
export class NewsletterModule {}
