<dx-data-grid
  [dataSource]="boletins"
  [allowColumnResizing]="true"
  [columnAutoWidth]="true"
  [showColumnLines]="false"
  [showRowLines]="false"
  [showBorders]="false"
  [rowAlternationEnabled]="true"
  [wordWrapEnabled]="true"
  [loadPanel]="false"
  [columnHidingEnabled]="true"
  [remoteOperations]="true"
  keyExpr="uuid"
>
  <dxo-paging [pageSize]="10"></dxo-paging>

  <dxo-pager
    [showInfo]="true"
    [showNavigationButtons]="true"
    [showPageSizeSelector]="false"
  ></dxo-pager>

  <dxo-header-filter [visible]="false"> </dxo-header-filter>
  <dxo-filter-row [visible]="true"></dxo-filter-row>
  <dxo-sorting mode="multiple"></dxo-sorting>
  <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

  <dxo-group-panel [visible]="false" [emptyPanelText]="''"></dxo-group-panel>

  <dxi-column
    dataField="titulo"
    caption="Título"
  ></dxi-column>

  <dxi-column
    dataField="data"
    caption="Data"
    cellTemplate="dateTemplate"
  ></dxi-column>
  <div *dxTemplate="let data of 'dateTemplate'">
    {{ data.value | date : 'dd/MM/YYYY - HH:mm' }}
  </div>

  <dxi-column
    dataField="uuid"
    caption=""
    [width]="120"
    [allowFiltering]="false"
    [allowSorting]="false"
    cellTemplate="actionColumn"
  ></dxi-column>

  <div *dxTemplate="let data of 'actionColumn'">
    <ng-container *ngIf="data.data.publicar === 'N'; else isPublished">
      <a
        (click)="modalConfirmarPublicacao(data.value)"
        class="dx-link dx-link-edit fas fa-paper-plane dx-link-icon btn-icon-grid"
      ></a>
      <a
        (click)="edit(data.value)"
        class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
      ></a>
      <a
        (click)="delete(data.value)"
        class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
      ></a>
    </ng-container>

    <ng-template #isPublished>
      <nb-badge text="Publicado" status="success"></nb-badge>
    </ng-template>
  </div>
</dx-data-grid>
