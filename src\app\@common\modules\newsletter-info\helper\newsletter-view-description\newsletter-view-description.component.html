<div *ngIf="loading; else content">
  <eqp-loading textLoading="Carregando boletins..."></eqp-loading>
</div>

<ng-template #content>
  <section class="boletim-container">
    <ng-container *ngIf="boletins.length > 0; else naoHaBoletins">
      <div class="boletim-item" *ngFor="let boletim of boletins; let i = index">
        <div class="boletim-content mt-2">
          <div class="d-flex justify-content-between">
            <div class="d-flex flex-column">
              <div class="info-row">
                <span class="label">Título:</span>
                <span class="value">{{ boletim.titulo }}</span>
              </div>
              <div class="info-row">
                <span class="label">Data:</span>
                <span class="value">{{
                  boletim.data | date : 'dd/MM/yyyy - HH:mm'
                }}</span>
              </div>
            </div>
            <div class="d-flex">
              <button
                class="view-button"
                nbButton
                status="primary"
                size="small"
                (click)="visualizarDescricao(boletim.uuid)"
              >
                <i class="fas fa-eye mr-1"></i>
                Visualizar descrição
              </button>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
    <ng-template #naoHaBoletins>
      <div class="not-found-newsletters">
        <p>Não há boletins cadastrados para o módulo selecionado.</p>
        <i class="fas fa-exclamation-circle"></i>
      </div>
    </ng-template>
  </section>
</ng-template>
