import { Component, Input, OnInit } from '@angular/core'
import { Dom<PERSON>anitizer, SafeHtml } from '@angular/platform-browser'
import { NbDialogRef } from '@nebular/theme'
import { NewsletterService } from '@pages/newsletter/newsletter.service'
import { finalize } from 'rxjs/operators'

@Component({
  selector: 'eqp-newsletter-view-message',
  templateUrl: './newsletter-view-message.component.html',
  styleUrls: ['./newsletter-view-message.component.scss'],
})
export class NewsletterViewMessageComponent implements OnInit {
  @Input() boletimUuid: string
  public loading = false
  public conteudoHtml: SafeHtml

  constructor(
    private newsletterService: NewsletterService,
    private sanitizer: DomSanitizer,
    private dialogRef: NbDialogRef<NewsletterViewMessageComponent>
  ) {}

  ngOnInit(): void {
    this.carregarBoletim()
  }

  private carregarBoletim() {
    this.loading = true
    this.newsletterService
      .getByUiid(this.boletimUuid)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(res => {
        this.conteudoHtml = this.sanitizer.bypassSecurityTrustHtml(
          res.dados.descricao,
        )
      })
  }

  public voltar() {
    this.dialogRef.close()
  }
}
