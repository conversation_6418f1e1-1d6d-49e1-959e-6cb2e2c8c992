<eqp-standard-page mainTitle="Boletim Informativo | Visualizar">
  <div class="container">
    <ng-container *ngFor="let modulo of modulos">
      <nb-accordion class="mb-1">
        <nb-accordion-item #accordion>
          <nb-accordion-item-header>
            {{ modulo.descricao }}
          </nb-accordion-item-header>
          <nb-accordion-item-body>
            <ng-container *ngIf="accordion.expanded">
              <eqp-newsletter-view-description
                [modulo]="modulo"
              ></eqp-newsletter-view-description>
            </ng-container>
          </nb-accordion-item-body>
        </nb-accordion-item>
      </nb-accordion>
    </ng-container>
  </div>
</eqp-standard-page>
