import { Component, Input, OnInit } from '@angular/core'
import { NbDialogService } from '@nebular/theme'
import { NewsletterInterface } from '@pages/newsletter/newsletter'
import { NewsletterService } from '@pages/newsletter/newsletter.service'
import { finalize } from 'rxjs/operators'
import { NewsletterViewMessageComponent } from '../newsletter-view-message/newsletter-view-message.component'
import { NewsletterStorageService } from '@core/utils/newsletter-storage.service'
import { normalizarString } from '@common/helpers/string-normalizer'

@Component({
  selector: 'eqp-newsletter-view-description',
  templateUrl: './newsletter-view-description.component.html',
  styleUrls: ['./newsletter-view-description.component.scss'],
})
export class NewsletterViewDescriptionComponent implements OnInit {
  @Input() modulo: any
  public loading = false
  public boletins: NewsletterInterface[] = []

  constructor(
    private newsletterService: NewsletterService,
    private newsletterStorageService: NewsletterStorageService,
    private dialogService: NbDialogService,
  ) {}

  ngOnInit(): void {
    const moduloLogadoNome = normalizarString(this.newsletterService.obterNomeModulo())
    const moduloNome = normalizarString(this.modulo.descricao)
    if(moduloLogadoNome && (moduloNome === moduloLogadoNome)) {
      this.newsletterStorageService.restore()
    }
    
    this.loading = true
    this.newsletterService
      .getAllByModule(this.modulo.uuid)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(boletins => {
        this.boletins = boletins
      })
  }

  visualizarDescricao(boletimUuid: string) {
    this.dialogService.open(NewsletterViewMessageComponent, {
      context: {
        boletimUuid,
      },
    })
  }
  
}
