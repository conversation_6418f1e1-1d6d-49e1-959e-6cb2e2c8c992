import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { ApiResponse } from '@core/data/api-response'
import { ResponseDto } from '@core/data/response-dto'
import { Observable } from 'rxjs'
import { NewsletterInterface } from './newsletter'
import { map } from 'rxjs/operators'
import { SistemaModuloNome } from './modules-enum'

function filterActiveNewsletters<T extends { publicar?: 'S' | 'N' }>(source$: Observable<T[]>): Observable<T[]> {
  return source$.pipe(
    map((arr: T[]) => arr.filter(item => item.publicar === 'S'))
  )
}

@Injectable({
  providedIn: 'root',
})
export class NewsletterService {
  private readonly MODULE_KEY = 'BACK_OFFICE'

  constructor(private _http: HttpClient) {
  }

  public loadModules() {
    const headers = new HttpHeaders()
    return this._http.get<ApiResponse<any>>('boletim-informativo/modulos?take=0', {
      headers,
    })
  }

  // mostra a quantidade de boletins não lidos para o módulo logado
  public loadTotalNewsletters() {
    const headers = new HttpHeaders()
      return this._http.get<ResponseDto<number>>(`boletim-informativo/notificacao/${this.MODULE_KEY}`, {
      headers,
    })
  }

  public getAll() {
    const headers = new HttpHeaders()
    return this._http.get<ApiResponse<NewsletterInterface>>('boletim-informativo?take=0', {
      headers,
    })
  }

  public getAllByModule(moduloUuid: string) {
    const headers = new HttpHeaders()
    const filter = ["moduloUuid","=",moduloUuid]
    let params = new HttpParams().append('filter', JSON.stringify(filter))
    return this._http.get<ApiResponse<NewsletterInterface>>('boletim-informativo', {
      headers,
      params
    }).pipe(
      map((res) => res.data),
      filterActiveNewsletters
    )
  }

  public getByUiid(uuid: string) {
    const headers = new HttpHeaders()

    return this._http.get<ResponseDto<NewsletterInterface>>(`boletim-informativo/${uuid}`, {
      headers,
    })
  }

  public put(dto: NewsletterInterface) {
    const headers = new HttpHeaders()

    return this._http.put<ApiResponse<NewsletterInterface>>(
      `boletim-informativo/${dto.uuid}`,
      dto,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public post(dto: NewsletterInterface) {
    const headers = new HttpHeaders()

    return this._http.post<ApiResponse<NewsletterInterface>>(
      'boletim-informativo',
      dto,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public delete(uuid: number | string): Observable<any> {
    const headers = new HttpHeaders()

    return this._http.delete<NewsletterInterface>(`boletim-informativo/${uuid}`, {
      headers,
    })
  }

  public publicar(uuid: number | string): Observable<any> {
    const headers = new HttpHeaders()

    return this._http.put<NewsletterInterface>(`boletim-informativo/publicar/${uuid}`, {
      headers,
    })
  }

  public listaPublicacao() {
    const headers = new HttpHeaders()

    return this._http.get<ApiResponse<any>>('boletim-informativo/lista-usuario', {
      headers,
    })
  }

  obterNomeModulo(): string {
    return SistemaModuloNome[this.MODULE_KEY]
  }
}
