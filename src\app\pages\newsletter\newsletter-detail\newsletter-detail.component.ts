import { AfterViewInit, Component, Input, OnInit } from '@angular/core'
import { NewsletterService } from '../newsletter.service'
import DataSource from 'devextreme/data/data_source'
import { first } from 'rxjs/operators'
import { NbDialogService } from '@nebular/theme'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { Router } from '@angular/router'
import { CrudService } from '@common/services/crud.service'
import { ConfirmationComponent } from '@common/dialogs/confirmation/confirmation.component'

@Component({
  selector: 'eqp-newsletter-detail',
  templateUrl: './newsletter-detail.component.html',
  styleUrls: ['./newsletter-detail.component.scss'],
})
export class NewsletterDetailComponent implements OnInit, AfterViewInit {
  @Input() moduloUuid: string
  public boletins: DataSource
  private readonly routeBase = 'boletim-informativo'

  constructor(
    private _service: NewsletterService,
    private _dialogService: NbDialogService,
    private _toastr: ToastrService,
    private _router: Router,
    private _crudService: CrudService
  ) {}

  ngOnInit(): void {}

  ngAfterViewInit(): void {
    this._carregarBoletins()
  }

  private _carregarBoletins() {
    this.boletins = new DataSource({
      store: this._crudService.getDataSourceFiltro(
        'uuid',
        'boletim-informativo',
        10,
        'moduloUuid',
        this.moduloUuid,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public modalConfirmarPublicacao(uuid: string) {
    this._dialogService.open(ConfirmationComponent, {
      context: {
        dialogTitle: 'Confirmar Publicação do Boletim',
        confirmationContent: {
          body: 'Após a publicação, não será possível editar ou cancelar o conteúdo.',
          confirmText: 'Publicar',
          confirmTitle: 'Confirmação de Publicação',
          confirmType: 'success',
          cancelText: 'Cancelar',
          cancelTitle: 'Cancelar',
        },
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    .onClose
      .pipe(first())
      .subscribe(retorno => {
        if (retorno) {
          this.publicar(uuid)
        }
      })
  }

  private publicar(uuid: string) {
    this._service.publicar(uuid).subscribe(() => {
      this._toastr.send({
        success: true,
        message: 'Boletim informativo publicado com sucesso.',
      })
      this.boletins.reload()
    })
  }

  public edit(uuid: string): void {
    this._router.navigate([`${this.routeBase}/edit/${uuid}`])
  }

  public delete(uuid: string) {
    const dialogRef = this._dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this._service
          .delete(uuid)
          .pipe(first())
          .subscribe(
            () => {
              this._toastr.send({
                success: true,
                message: 'Boletim informativo excluído com sucesso.',
              })
              this._carregarBoletins()
            },
            (resp: any) => this._toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
